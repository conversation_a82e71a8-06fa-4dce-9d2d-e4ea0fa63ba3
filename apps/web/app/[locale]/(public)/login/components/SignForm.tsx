"use client"

import { LoginForm, refreshSession } from "@repo/auth/react"
import { useRouter } from "@repo/i18n"

export function SignForm() {
	const router = useRouter()
	return (
		<LoginForm
			providers={
				process.env.NODE_ENV === "development" ? ["email"] : ["wechat-mp"]
			}
			showPolicies={true}
			termsUrl="/terms-of-services"
			privacyUrl="/privacy-policy"
			onSuccess={(data?: Record<string, unknown>) => {
				console.log("Login success:", data)
				refreshSession()
				router.push("/dashboard")
			}}
			onError={(error: Error) => {
				console.error("Login error:", error)
			}}
		/>
	)
}
