"use client"

import { useTranslationContext } from "@/lib/components/translation/TranslationContext"
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	Input,
	Label,
	Textarea,
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
	Button,
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@repo/ui/components"
import { Globe, HelpCircle } from "lucide-react"
import { TranslationSwitcher } from "@/lib/components/translation/TranslationSwitcher"
import { useSEOMetadata } from "../hooks/useSEOMetadata"
import { useState, useEffect } from "react"
import { toast } from "sonner"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { fetchPost } from "@repo/utils/react"
import { useCallback } from "react"
import { ProjectLanguage } from "@repo/shared-types"
import { ProjectLocaleSiteSettingType } from "@repo/shared-types"

// 定义表单验证模式
const formSchema = z.object({
	title: z.string().max(60, {
		message: "标题不能超过60个字符",
	}),
	description: z.string().max(160, {
		message: "描述不能超过160个字符",
	}),
})

// 定义组件Props类型
interface SEOSettingsProps {
	projectId: string
}

export default function SEOSettings({ projectId }: SEOSettingsProps) {
	// 获取翻译上下文
	const { currentLanguage, defaultLanguage, languages } = useTranslationContext()

	// 使用SEO元数据hook
	const { isLoading, isSaving, seoMetadata, updateSEOMetadata, refreshSEO } =
		useSEOMetadata(projectId)

	// 表单状态
	const [isSubmitting, setIsSubmitting] = useState(false)

	// 初始化表单
	const form = useForm<z.infer<typeof formSchema>>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			title: "",
			description: "",
		},
	})

	// 当SEO数据加载完成后，更新表单默认值
	useEffect(() => {
		if (!isLoading && seoMetadata) {
			form.reset({
				title: seoMetadata.title || "",
				description: seoMetadata.description || "",
			})
		}
	}, [isLoading, seoMetadata, form])

	// 提交表单
	const onSubmit = async (values: z.infer<typeof formSchema>) => {
		setIsSubmitting(true)
		try {
			const success = await updateSEOMetadata({
				title: values.title,
				description: values.description,
			})

			if (success) {
				toast.success("SEO设置已保存")
				form.reset(values)
			} else {
				toast.error("保存失败，请重试")
			}
		} catch (error) {
			console.error("保存SEO设置失败:", error)
			toast.error("保存失败，请重试")
		} finally {
			setIsSubmitting(false)
		}
	}

	// 重置表单
	const handleReset = () => {
		if (seoMetadata) {
			form.reset({
				title: seoMetadata.title || "",
				description: seoMetadata.description || "",
			})
			toast.success("已重置为保存的设置")
		} else {
			refreshSEO()
			toast.success("已重新加载设置")
		}
	}

	// 翻译功能
	const translate = useCallback(
		async (selectedLanguages: ProjectLanguage[]) =>
			fetchPost("/api/project-locale-site-settings/translate", {
				projectId,
				sourceLocale: defaultLanguage,
				targetLocales: selectedLanguages,
				type: ProjectLocaleSiteSettingType.Metadata,
			}),
		[projectId, defaultLanguage],
	)

	return (
		<>
			{/* SEO设置 */}
			<Card className="mb-6">
				<CardHeader>
					<div className="flex items-center justify-between">
						<CardTitle className="flex items-center gap-2">
							<Globe className="h-5 w-5 text-primary" />
							<span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
								SEO 设置
							</span>
						</CardTitle>
					</div>
					<TranslationSwitcher onTranslate={translate} />
				</CardHeader>
				<CardContent>
					{isLoading ? (
						<div className="py-8 text-center text-muted-foreground">
							加载中...
						</div>
					) : (
						<Form {...form}>
							<form
								onSubmit={form.handleSubmit(onSubmit)}
								className="space-y-6"
							>
								<FormField
									control={form.control}
									name="title"
									render={({ field }) => (
										<FormItem className="space-y-2">
											<div className="flex items-center gap-2">
												<FormLabel htmlFor="seo-title">网站标题</FormLabel>
												<TooltipProvider>
													<Tooltip>
														<TooltipTrigger asChild>
															<HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
														</TooltipTrigger>
														<TooltipContent>
															<p className="max-w-xs">
																网站标题将显示在浏览器标签页和搜索引擎结果中，建议控制在60个字符以内
															</p>
														</TooltipContent>
													</Tooltip>
												</TooltipProvider>
											</div>
											<FormControl>
												<Input
													id="seo-title"
													placeholder="输入网站标题"
													maxLength={60}
													disabled={isSubmitting || isSaving}
													{...field}
												/>
											</FormControl>
											<p className="text-xs text-muted-foreground mt-1">
												{field.value?.length || 0}/60
											</p>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="description"
									render={({ field }) => (
										<FormItem className="space-y-2">
											<div className="flex items-center gap-2">
												<FormLabel htmlFor="seo-description">
													网站描述
												</FormLabel>
												<TooltipProvider>
													<Tooltip>
														<TooltipTrigger asChild>
															<HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
														</TooltipTrigger>
														<TooltipContent>
															<p className="max-w-xs">
																网站描述将显示在搜索引擎结果中，建议控制在160个字符以内
															</p>
														</TooltipContent>
													</Tooltip>
												</TooltipProvider>
											</div>
											<FormControl>
												<Textarea
													id="seo-description"
													placeholder="输入网站描述"
													className="min-h-[100px]"
													maxLength={160}
													disabled={isSubmitting || isSaving}
													{...field}
												/>
											</FormControl>
											<p className="text-xs text-muted-foreground mt-1">
												{field.value?.length || 0}/160
											</p>
											<FormMessage />
										</FormItem>
									)}
								/>

								<div className="fixed bottom-0 left-0 right-0 bg-background/80 backdrop-blur-sm border-t border-border py-3 sm:py-4 px-4 sm:px-6 z-10 shadow-lg">
									<div className="max-w-6xl mx-auto flex justify-end w-full gap-2">
										<Button
											type="button"
											variant="outline"
											onClick={handleReset}
											disabled={isSubmitting || isSaving || isLoading}
										>
											重置
										</Button>
										<Button
											type="submit"
											disabled={
												isSubmitting ||
												isSaving ||
												isLoading ||
												!form.formState.isDirty
											}
										>
											{isSubmitting ? "保存中..." : "保存设置"}
										</Button>
									</div>
								</div>
							</form>
						</Form>
					)}
				</CardContent>
			</Card>
		</>
	)
}
