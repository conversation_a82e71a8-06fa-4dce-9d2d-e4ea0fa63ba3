"use client"

import { useTranslationContext } from "@/lib/components/translation/TranslationContext"
import { ProjectLocaleSiteSettingType } from "@repo/shared-types"
import { fetchGet, fetchPost } from "@repo/utils/react"
import { useEffect, useRef, useState } from "react"
import { toast } from "sonner"
import useSWR from "swr"

/**
 * SEO元数据类型定义
 */
export interface SEOMetadata {
	title: string
	description: string
	locale: string
	code?: string // 可选，用于标识不同的SEO元数据
}

/**
 * 获取项目SEO元数据的Hook
 */
export function useSEOMetadata(projectId: string) {
  // 使用 LanguageContext 获取语言信息
  const { defaultLanguage, currentLanguage, languages, openTranslationDialog } =
    useTranslationContext()

	// 加载状态
	const [isLoading, setIsLoading] = useState<boolean>(true)
	const [isSaving, setIsSaving] = useState<boolean>(false)

	// 存储默认语言的SEO数据
	const [defaultLanguageSEO, setDefaultLanguageSEO] = useState<SEOMetadata>({
		title: "",
		description: "",
		locale: defaultLanguage,
	})

	// 存储当前语言的SEO数据
	const [currentLanguageSEO, setCurrentLanguageSEO] =
		useState<SEOMetadata | null>(null)

	// 标记是否已加载默认语言数据
	const hasLoadedDefaultLanguage = useRef(false)

	// 跟踪上一次请求的语言
	const [lastRequestedLanguage, setLastRequestedLanguage] = useState<
		string | null
	>(null)

	// 获取默认语言的SEO数据
	const { mutate: refreshDefaultSEO } = useSWR<any>(
		defaultLanguage
			? `/api/project-locale-site-settings?projectId=${projectId}&locale=${defaultLanguage}&type=${ProjectLocaleSiteSettingType.Metadata}`
			: null,
		fetchGet,
		{
			revalidateOnFocus: false,
			revalidateOnReconnect: false,
			onSuccess: (data) => {
				const defaultSEO = data?.content || {
					title: "",
					description: "",
					locale: defaultLanguage,
				}

				// 确保默认SEO数据包含locale字段
				if (!defaultSEO.locale) {
					defaultSEO.locale = defaultLanguage
				}

				setDefaultLanguageSEO(defaultSEO)
				hasLoadedDefaultLanguage.current = true

				// 如果当前语言就是默认语言，直接设置加载完成
				if (currentLanguage === defaultLanguage) {
					setCurrentLanguageSEO(defaultSEO)
					setIsLoading(false)
				}
			},
			onError: (error) => {
				console.error("加载默认语言SEO数据失败:", error)
				// 设置默认值
				setDefaultLanguageSEO({
					title: "",
					description: "",
					locale: defaultLanguage,
				})
				hasLoadedDefaultLanguage.current = true
				setIsLoading(false)
			},
		},
	)

	// 使用 useSWR 加载当前语言的SEO数据（仅当不是默认语言时）
	const { isValidating, mutate: refreshCurrentLanguage } = useSWR<any>(
		currentLanguage &&
			currentLanguage !== defaultLanguage &&
			hasLoadedDefaultLanguage.current
			? `/api/project-locale-site-settings?projectId=${projectId}&locale=${currentLanguage}&type=${ProjectLocaleSiteSettingType.Metadata}`
			: null,
		fetchGet,
		{
			revalidateOnFocus: false,
			revalidateOnReconnect: false,
			onSuccess: (data) => {
				// 请求成功后更新上一次请求的语言
				setLastRequestedLanguage(currentLanguage)

				// 处理当前语言的SEO数据
				const currentSEO = data?.content || null

				if (currentSEO) {
					// 确保当前SEO数据包含locale字段
					if (!currentSEO.locale) {
						currentSEO.locale = currentLanguage
					}
					setCurrentLanguageSEO(currentSEO)
				} else {
					// 如果没有当前语言的数据，创建一个基于默认语言的新数据结构
					setCurrentLanguageSEO({
						title: "",
						description: "",
						locale: currentLanguage,
					})
				}

				setIsLoading(false)
			},
			onError: (error) => {
				console.error("加载当前语言SEO数据失败:", error)
				// 创建一个空的SEO数据结构
				setCurrentLanguageSEO({
					title: "",
					description: "",
					locale: currentLanguage,
				})
				setIsLoading(false)
			},
		},
	)

	// 当语言变化或验证状态变化时更新加载状态
	useEffect(() => {
		// 当语言变化时设置加载状态
		if (currentLanguage && currentLanguage !== lastRequestedLanguage) {
			setIsLoading(true)

			// 如果切换到默认语言，使用默认语言的SEO数据
			if (currentLanguage === defaultLanguage) {
				setCurrentLanguageSEO(defaultLanguageSEO)
				setIsLoading(false)
			} else if (hasLoadedDefaultLanguage.current) {
				// 如果已经加载了默认语言数据，尝试加载当前语言的数据
				refreshCurrentLanguage()
			}
		}
		// 如果不在验证中，且上一次请求的语言与当前语言相同，则设置为非加载状态
		else if (!isValidating && lastRequestedLanguage === currentLanguage) {
			setIsLoading(false)
		}
	}, [
		currentLanguage,
		lastRequestedLanguage,
		defaultLanguage,
		isValidating,
		defaultLanguageSEO,
		refreshCurrentLanguage,
	])

	// 保存SEO数据
	const saveSEOMetadata = async (seoData: SEOMetadata): Promise<boolean> => {
		try {
			setIsSaving(true)

			// 确保SEO数据包含当前语言
			const dataToSave = {
				...seoData,
				locale: currentLanguage,
			}

			// 发送请求保存SEO数据
			const response = await fetchPost(`/api/project-locale-site-settings`, {
				projectId,
				locale: currentLanguage,
				type: ProjectLocaleSiteSettingType.Metadata,
				content: dataToSave,
			})

			// 更新本地状态
			if (response.content) {
				if (currentLanguage === defaultLanguage) {
					setDefaultLanguageSEO(response.content)
					setCurrentLanguageSEO(response.content)
				} else {
					setCurrentLanguageSEO(response.content)
				}
			}

			return true
		} catch (error) {
			console.error("保存SEO数据失败:", error)
			return false
		} finally {
			setIsSaving(false)
		}
	}

	// 更新SEO数据
	const updateSEOMetadata = async (updatedSEO: Partial<SEOMetadata>) => {
		// 获取当前编辑的SEO数据
		const currentSEO = getCurrentSEO()

		if (!currentSEO) return false

		// 合并更新的数据
		const newSEO: SEOMetadata = {
			...currentSEO,
			...updatedSEO,
			locale: currentLanguage, // 确保使用当前语言
		}

		// 保存到服务器
		try {
			const success = await saveSEOMetadata(newSEO)

			if (success) {
				toast.success("SEO设置保存成功")

				// 如果是默认语言，询问是否需要翻译
				if (currentLanguage === defaultLanguage && languages.length > 1) {
					// 打开翻译对话框
					openTranslationDialog()
				}

				return true
			} else {
				toast.error("SEO设置保存失败")
				return false
			}
		} catch (error) {
			console.error("保存SEO设置失败:", error)
			toast.error("SEO设置保存失败")
			return false
		}
	}

	// 获取当前应该编辑的SEO数据
	const getCurrentSEO = (): SEOMetadata => {
		if (currentLanguage === defaultLanguage) {
			return defaultLanguageSEO
		} else {
			// 如果有当前语言的SEO数据，返回当前语言的数据
			if (currentLanguageSEO) {
				return currentLanguageSEO
			}
			// 如果没有当前语言的SEO数据，返回一个基于默认语言的新数据结构
			return {
				title: "",
				description: "",
				locale: currentLanguage,
			}
		}
	}

	// 获取当前编辑的SEO数据
	const currentSEO = getCurrentSEO()

	return {
		isLoading,
		isSaving,
		seoMetadata: currentSEO,
		updateSEOMetadata,
		refreshSEO:
			currentLanguage === defaultLanguage
				? refreshDefaultSEO
				: refreshCurrentLanguage,
	}
}
