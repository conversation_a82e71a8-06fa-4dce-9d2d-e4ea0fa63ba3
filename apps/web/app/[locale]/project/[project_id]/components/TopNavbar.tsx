"use client"

import { UserAuthSection } from "@lib/components/UserAuthSection"
import { useRefreshSession } from "@lib/hooks/useRefreshSession"
import { useProjectPreview } from "@lib/hooks/useProjectPreview"
import { Link } from "@repo/i18n"
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
    Button
} from "@repo/ui/components"
import { Menu, Eye } from "lucide-react"
import { usePathname } from "next/navigation"
import { useTranslations } from "next-intl"
import React from "react"
import { DeploymentStatus } from "@repo/shared-types"
type TopNavbarProps = {
	projectId: string
	projectName: string
}

type NavBreadcrumbItem = {
	label: string
	href: string
	current?: boolean
}

const TopNavbar = ({ projectId, projectName }: TopNavbarProps) => {
	useRefreshSession({
		refreshOnFocus: false,
	})

	const pathname = usePathname()
	const t = useTranslations("Preview")

	// 使用预览钩子
	const {
		openPreviewModal,
		PreviewModal,
		previewRecord
	} = useProjectPreview({ projectId })

	// 生成面包屑导航
	const generateBreadcrumbs = (): NavBreadcrumbItem[] => {
		// 面包屑生成
		const pageLabels: {[key: string]: string} = {
			"site-settings": "网站设置",
			"categories": "分类管理",
			"games": "游戏列表",
			"new-game": "添加游戏",
			"articles": "文章管理",
			"menu-settings": "菜单设置",
			"ad-settings": "广告设置",
			"deployment": "部署管理",
			"domain-binding": "域名绑定"
		}
		const getBreadcrumbs = () => {
			const paths = pathname.split("/").filter(Boolean)
			return paths.map((path, index) => ({
				label: pageLabels[path] || "",
				current: index === paths.length - 1,
				href: `/${paths.slice(0, index + 1).join("/")}`,
			})).filter((it)=>it.label!=='');
		}
		const breadcrumbs: NavBreadcrumbItem[] = getBreadcrumbs()

		// // 添加项目管理
		// breadcrumbs.push({
		// 	label: "网站设置",
		// 	href: "/project/"
		// })

		// // 添加项目名称
		// breadcrumbs.push({
		// 	label: projectName,
		// 	href: `/${pathSegments.slice(0, 3).join("/")}`
		// })

		// // 添加当前页面
		// if (pathSegments.length > 3) {
		// 	const lastSegment = pathSegments[pathSegments.length - 1]
			

		// 	breadcrumbs.push({
        // // @ts-ignore
		// 		label: pageLabels[lastSegment] || lastSegment,
		// 		href: pathname,
		// 		current: true
		// 	})
		// }

		return breadcrumbs
	}

	const breadcrumbs = generateBreadcrumbs()

	return (
		<header className="bg-background shadow-sm px-5 py-2 border-b border-border">
			<div className="flex justify-between items-center">
				<div className="flex items-center gap-4">
					{/* 移动端汉堡菜单按钮 */}
					<Button
						variant="ghost"
						size="icon"
						className="md:hidden"
						onClick={() => {
							// 在移动端点击时，切换侧边栏的显示/隐藏
							const sidebarContainer = document.querySelector('.sidebar-container');
							if (sidebarContainer) {
								if (sidebarContainer.classList.contains('-translate-x-full')) {
									// 显示侧边栏
									sidebarContainer.classList.remove('-translate-x-full');
									sidebarContainer.classList.add('translate-x-0');

									// 添加遮罩层
									const overlay = document.createElement('div');
									overlay.className = 'fixed inset-0 bg-black/50 z-30 opacity-100 transition-opacity duration-300';
									overlay.id = 'sidebar-overlay';
									overlay.onclick = () => {
										sidebarContainer.classList.remove('translate-x-0');
										sidebarContainer.classList.add('-translate-x-full');
										document.getElementById('sidebar-overlay')?.remove();
									};
									document.body.appendChild(overlay);
								} else {
									// 隐藏侧边栏
									sidebarContainer.classList.remove('translate-x-0');
									sidebarContainer.classList.add('-translate-x-full');
									// 移除遮罩层
									document.getElementById('sidebar-overlay')?.remove();
								}
							}
						}}
					>
						<Menu className="h-5 w-5" />
					</Button>

					{/* 面包屑导航 */}
					<Breadcrumb>
						<BreadcrumbList>
							{breadcrumbs.map((crumb, index) => (
								<React.Fragment key={index}>
									{index > 0 && <BreadcrumbSeparator />}
									<BreadcrumbItem>
										{crumb.current ? (
											<BreadcrumbPage>{crumb.label}</BreadcrumbPage>
										) : (
											<BreadcrumbLink asChild>
												<Link href={crumb.href}>{crumb.label}</Link>
											</BreadcrumbLink>
										)}
									</BreadcrumbItem>
								</React.Fragment>
							))}
						</BreadcrumbList>
					</Breadcrumb>
				</div>

				<div className="flex items-center space-x-4 mr-6">
					<div className="flex items-center space-x-4">
						{/* 预览按钮 */}
						<Button
							variant="outline"
							size="sm"
							onClick={openPreviewModal}
							className="flex items-center gap-1"
						>
							<Eye className="h-4 w-4" />
							{t("preview")}
							{previewRecord && (previewRecord.status === DeploymentStatus.Deploying) && (
								<span className="ml-1 w-2 h-2 rounded-full bg-green-500"></span>
							)}
							{previewRecord && (previewRecord.status === DeploymentStatus.Pending) && (
								<span className="ml-1 w-2 h-2 rounded-full bg-yellow-500"></span>
							)}
						</Button>
					</div>
					<UserAuthSection variant="light" />
				</div>
			</div>
			{/* 预览模态框 */}
			{PreviewModal}
		</header>
	)
}

export default TopNavbar
