import "../../theme-vars.css"
import "../../menu-styles.css"

import {
	getMessages,
	getTranslations,
	setRequestLocale,
} from "next-intl/server"
import type { ReactNode } from "react"

import { siteConfig } from "@lib/config/site"
import { authUser } from "@repo/auth/server"
import { prisma } from "@repo/db"
import { alternatesLanguage } from "@repo/i18n"
import type { Metadata } from "next"
import { notFound, redirect } from "next/navigation"

import { AuthContextProvider } from "@repo/auth/react"
import { NextIntlClientProvider } from "next-intl"
import Sidebar from "./components/Sidebar"
import TopNavbar from "./components/TopNavbar"
import { Toaster } from "sonner"
import { ProjectProvider } from "./context/ProjectProvider"
type Props = {
	children: ReactNode
	params: Promise<{ locale: string; project_id: string }>
}

export async function generateMetadata({
	params,
}: {
	params: Promise<{ locale: string }>
}): Promise<Metadata> {
	const { locale } = await params
	setRequestLocale(locale)
	const t = await getTranslations({ locale: locale })
	return {
		title: `${t("title")} | ${t("slogan")}`,
		description: t("description"),
		alternates: {
			languages: alternatesLanguage(""),
		},
		icons: {
			icon: siteConfig.icon,
			apple: siteConfig.appleIcon,
		},
		robots: {
			index: false,
			follow: true,
		},
	}
}

export default async function Layout({ children, params }: Props) {
	const { locale, project_id } = await params
	const messages = await getMessages()

	const userInfo = (await authUser())!
	if (!userInfo) {
		return redirect(`/${locale}/login`)
	}
	const dbUser = await prisma.user.findUnique({
		where: {
			id: userInfo?.id,
		},
	})
	if (!dbUser) {
		return redirect(`/${locale}/login`)
	}

	// 检查当前用户是否是项目所有者
	const project = await prisma.project.findFirst({
		where: {
			id: project_id,
		},
	})

	// 如果不是项目所有者，检查是否有访问权限
	if (!project) {
		return notFound()
	}

	// 用户是项目所有者
	return (
		<NextIntlClientProvider messages={messages} locale={locale}>
			<AuthContextProvider>
				<ProjectProvider initialProject={project}>
					<Toaster position="top-center" />
					<div className="flex h-screen bg-background">
						<div className="md:translate-x-0 -translate-x-full md:static fixed z-40 transition-transform duration-300">
							<Sidebar
								project={project}
								isOwner={project.userId === userInfo?.id}
							/>
						</div>
						<div className="flex flex-col flex-1 overflow-hidden">
							<TopNavbar
								projectId={project_id}
								projectName={project?.name as string}
							/>
							<main className="min-h-[calc(100vh-4rem)] max-w-full bg-gray-100 overflow-y-auto box-content ">
								{children}
							</main>
						</div>
					</div>
				</ProjectProvider>
			</AuthContextProvider>
		</NextIntlClientProvider>
	)
}
