"use client"

import { useTranslations } from "next-intl"
import {
	Input,
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
	Button,
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormDescription,
	FormMessage,
	Switch,
	RadioGroup,
	RadioGroupItem,
} from "@repo/ui/components"

import {
	ProjectLocaleSiteSettingType,
	GameCategory,
	GameTag,
	GameType,
} from "@repo/shared-types"
import { useState, useEffect, useMemo } from "react"
import { Video, Plus, X, Eye, Play, Trash } from "lucide-react"
import ReactPlayer from "react-player"
import { Icon } from "@/lib/components/icons/Icon"

import { fetchGet } from "@repo/utils/react"
import { useForm } from "react-hook-form"
import ImageUploader from "@/lib/components/ImageUploader"
import { MultiSelect, MultiSelectItem } from "./MultiSelect"
import { useTranslationContext } from "@/lib/components/translation/TranslationContext"
import { IframePreviewComponent } from "@repo/web/lib/components/game/IframePreviewComponent"
import {
	useGameBasicInfoForm,
	GameBasicInfoData,
} from "../../hooks/useGameBasicInfoForm"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import { GameTagSelector } from "@lib/components/game/GameTagSelector"
import { toast } from "sonner"
import { useRouter } from "@repo/i18n"

// 自定义 Zod 错误信息 - 用于全局通用错误
const customErrorMap: z.ZodErrorMap = (issue, ctx) => {
	// 只处理通用错误，特定字段的错误在 schema 中定义
	if (issue.code === z.ZodIssueCode.invalid_type) {
		if (issue.expected === "string") {
			return { message: "请输入有效的文本" }
		}
		if (issue.expected === "array") {
			return { message: "请选择至少一项" }
		}
	}

	// 如果没有在 schema 中定义特定错误信息，则使用这些默认消息
	if (issue.code === z.ZodIssueCode.too_small) {
		if (issue.type === "array") {
			return { message: "请至少选择一项" }
		}
		if (issue.type === "string") {
			return { message: "此字段不能为空" }
		}
	}

	// 对于其他错误，使用默认错误信息
	return { message: ctx.defaultError }
}

// 设置 Zod 使用自定义错误映射
// 注意：这会影响所有 Zod schema，但特定字段的错误信息会覆盖这里的设置
z.setErrorMap(customErrorMap)

import useSWR from "swr"

interface BasicInfoTabProps {
	projectId: string
	gameId: string
}
// 创建一个条件验证的 schema
const gameDownloadSettingsSchema = z.union([
	// 当游戏类型为 Download 时，使用这个 schema
	z.object({
		showDownloadButton: z.boolean().optional(),
		downloadUrls: z
			.object({
				pc: z.string().optional(),
				mac: z.string().optional(),
				android: z.string().optional(),
				ios: z.string().optional(),
				steam: z.string().optional(),
			})
			.optional(),
	}),
	// 当游戏类型不是 Download 时，使用这个 schema
	z.any(),
])

// 使用更简单的方法处理可能为空的字段
const nullOrString = z.union([z.string(), z.null(), z.literal("")])
const nullOrStringArray = z.union([
	z.array(z.string()),
	z.null(),
	z.literal(""),
])

const formSchema = z
	.object({
		id: z.string().optional(),
		// 处理可能为空字符串或 null 的字段
		publicGameLibraryId: nullOrString.optional(),
		name: z
			.string({
				required_error: "游戏名称不能为空",
				invalid_type_error: "游戏名称不能为空",
			})
			.min(1, { message: "游戏名称不能为空" }),
		slug: z
			.string({
				required_error: "游戏访问路径不能为空",
				invalid_type_error: "游戏访问路径格式不正确,只允许英文、数字、-",
			})
			.min(1, { message: "游戏访问路径不能为空" }),
		categories: z.array(z.string()).optional(),
		// 处理可能为空字符串或 null 的字段
		userInputText: nullOrString.optional(),
		isPrimary: z.boolean().optional(),
		// 处理可能为 null 或其他类型的设置
		settings: z.any().optional(),
		tags: z.array(z.string()).optional(),
		gameType: z.nativeEnum(GameType).optional(),
		gameDownloadSettings: gameDownloadSettingsSchema.optional(),
		backgroundSettings: z
			.object({
				show: z.boolean().optional(),
				type: z.enum(["image", "video"]).optional(),
				imageUrl: nullOrString.optional(),
				videoUrl: nullOrString.optional(),
				backgroundColor: nullOrString.optional(),
			})
			.optional(),
		// 处理可能为空字符串的 URL 字段
		gameIframeUrl: z
			.union([
				z
					.string()
					.url({ message: "请输入有效的URL" })
					.refine(
						(url) => url.startsWith("http://") || url.startsWith("https://"),
						{ message: "URL必须以http://或https://开头" },
					),
				z.null(),
				z.literal(""),
			])
			.optional(),
		// 游戏封面图是可选的
		screenshotUrl: nullOrString.optional(),
		// 处理可能为 null 或空数组的相关视频
		relatedVideos: z
			.array(
				z
					.string()
					.url({ message: "请输入有效的视频URL" })
					.refine(
						(url) => url.startsWith("http://") || url.startsWith("https://"),
						{ message: "视频URL必须以http://或https://开头" },
					),
			)
			.optional(),
	})
	.refine(
		(data) => {
			// 当游戏类型为 Iframe 或 Popup 时才检查 gameIframeUrl
			if (
				data.gameType &&
				(data.gameType === GameType.Iframe ||
					data.gameType === GameType.Popup) &&
				!data.gameIframeUrl
			) {
				return false
			}
			return true
		},
		{
			message: "Iframe嵌入或页面弹窗类型游戏必须填写游戏Iframe链接",
			path: ["gameIframeUrl"],
		},
	)
	.refine(
		(data) => {
			// 只有当游戏类型为 Download 时才检查 gameDownloadSettings
			if (data.gameType && data.gameType === GameType.Download) {
				return !!data.gameDownloadSettings
			}
			return true
		},
		{
			message: "下载类型游戏必须填写下载设置",
			path: ["gameDownloadSettings"],
		},
	)

type FormData = z.infer<typeof formSchema>

export default function BasicInfoTab({ projectId, gameId }: BasicInfoTabProps) {
	const t = useTranslations("Games")
	const { currentLanguage } = useTranslationContext()
	const [newVideoUrl, setNewVideoUrl] = useState("")
	const [previewIframeUrl, setPreviewIframeUrl] = useState("")
	const router = useRouter()
	const { data, handleSave, isLoading } = useGameBasicInfoForm(
		projectId,
		gameId,
	)

	// 设置表单
	const form = useForm<FormData>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			gameType: GameType.Iframe,
		} as FormData,
		mode: "onChange", // 实时验证
		criteriaMode: "all", // 显示所有错误
	})

	// 当数据加载完成后更新表单
	useEffect(() => {
		if (data) {
			form.reset(data as FormData)
		}
	}, [data, form])

	// 获取表单数据
	const formData = form.watch()

	const { data: categories = [], isLoading: isLoadingCategories } = useSWR<
		GameCategory[]
	>(
		`/api/project-locale-site-settings?projectId=${projectId}&type=${ProjectLocaleSiteSettingType.GameCategories}&locale=${currentLanguage}`,
		(url) => fetchGet<any>(url).then((data) => data.content ?? []),
		{
			refreshInterval: 0,
			revalidateOnMount: true,
			fallbackData: [],
		},
	)

	// 使用 form.watch 获取需要的字段
	const name = form.watch("name")
	const backgroundSettings = form.watch("backgroundSettings")
	const gameType = form.watch("gameType")
	const relatedVideos = form.watch("relatedVideos") || []

	// 当名称变化时，自动生成 slug
	useEffect(() => {
		if (name) {
			form.setValue("slug", `${name.toLowerCase().replace(/ /g, "-")}`)
		}
	}, [name, form])

	// 当游戏类型变化时，处理相关字段
	useEffect(() => {
		// 当游戏类型不是 Download 时，清空 gameDownloadSettings
		if (gameType !== GameType.Download) {
			form.setValue("gameDownloadSettings", undefined)
		}

		// 当游戏类型不是 Iframe 时，清空 gameIframeUrl
		if (gameType !== GameType.Iframe) {
			form.setValue("gameIframeUrl", undefined)
		}
		if (gameId !== "new-game") {
			// 触发表单重新验证
			form.trigger()
		}
	}, [gameType, form])

	// 视频相关操作

	const handleAddVideo = (url: string) => {
		if (!url) return

		// 验证URL格式
		const urlRegex = /^(https?:\/\/)/i
		if (!urlRegex.test(url)) {
			toast.error(t("videoUrlMustStartWithHttpOrHttps"))
			return
		}

		try {
			// 检查是否是有效的URL
			new URL(url)

			// 检查是否已经存在相同的URL
			if (relatedVideos.includes(url)) {
				toast.error(t("videoUrlAlreadyExists"))
				return
			}

			form.setValue("relatedVideos", [...relatedVideos, url])
			setNewVideoUrl("")
		} catch (error) {
			toast.error(t("invalidVideoUrl"))
		}
	}

	const handleRemoveVideo = (videoUrl: string) => {
		form.setValue(
			"relatedVideos",
			relatedVideos.filter((url) => url !== videoUrl),
		)
	}

	const onSubmit = async (data: z.infer<typeof formSchema>) => {
		try {
			// 处理表单数据：使用解构和重组对象的方式，避免使用 delete 操作符
			const {
				gameType,
				gameDownloadSettings,
				screenshotUrl,
				publicGameLibraryId,
				userInputText,
				settings,
				relatedVideos,
				...restData
			} = data

			// 处理可选字段，将空字符串和 null 转换为 undefined
			const processedData = {
				...restData,
				gameType,
				// 处理可能为空字符串或 null 的字段
				screenshotUrl:
					screenshotUrl === "" || screenshotUrl === null
						? undefined
						: screenshotUrl,
				publicGameLibraryId:
					publicGameLibraryId === "" || publicGameLibraryId === null
						? undefined
						: publicGameLibraryId,
				userInputText:
					userInputText === "" || userInputText === null
						? undefined
						: userInputText,
				settings: settings === null ? undefined : settings,
				// 确保 relatedVideos 是数组
				relatedVideos: Array.isArray(relatedVideos) ? relatedVideos : [],
			}

			// 根据游戏类型决定是否包含 gameDownloadSettings
			const formData =
				gameType === GameType.Download
					? { ...processedData, gameDownloadSettings }
					: processedData

			await handleSave(formData as GameBasicInfoData)
			router.replace(`/project/${projectId}/games/${gameId}`)
		} catch (error) {
			console.error(error)
			toast.error(error as string)
		}
	}

	// 处理表单提交错误
	const onError = (errors: any) => {
		console.error("表单验证错误:", errors)
		// 显示错误提示
		if (Object.keys(errors).length > 0) {
			toast.error("表单填写有误，请检查并修正错误")
		}
	}
	// 修改函数签名以接受可能为 null 的值
	const handlePreviewIframe = (url: string | null | undefined) => {
		if (url) {
			setPreviewIframeUrl(url)
		}
	}

	return (
		<Form {...form}>
			{isLoading ? (
				<div className="space-y-8">
					<div className="bg-card rounded-lg p-6">
						<div className="flex justify-between items-center mb-6">
							<div className="h-6 w-32 bg-muted animate-pulse rounded" />
							<div className="h-6 w-24 bg-muted animate-pulse rounded" />
						</div>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
							{[1, 2, 3, 4].map((i) => (
								<div key={i} className="space-y-2">
									<div className="h-4 w-24 bg-muted animate-pulse rounded" />
									<div className="h-10 w-full bg-muted animate-pulse rounded" />
								</div>
							))}
						</div>
					</div>
				</div>
			) : (
				<form onSubmit={form.handleSubmit(onSubmit, onError)} noValidate>
					<div className="bg-card rounded-lg">
						<div className="flex justify-between items-center mb-6">
							<h2 className="text-xl font-semibold flex items-center gap-2">
								<Icon name="info" size={20} />
								{t("gameBasicInfo")}
							</h2>
						</div>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
							<FormField
								control={form.control}
								name="name"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="after:content-['*'] after:ml-0.5 after:text-red-500">
											{t("gameName")}
										</FormLabel>
										<FormControl>
											<Input
												placeholder={t("enterGameName")}
												{...field}
												onChange={(e) => {
													field.onChange(e)
												}}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="slug"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="after:content-['*'] after:ml-0.5 after:text-red-500">
											{t("gameSlug")}
										</FormLabel>
										<FormControl>
											<div className="flex items-center justify-start px-3">
												<div className="flex items-center bg-muted h-full px-3 border border-r-0 rounded-l-md text-sm text-muted-foreground">
													/games/
												</div>
												<Input
													className="rounded-l-none"
													placeholder={t("enterGameSlug")}
													{...field}
												/>
											</div>
										</FormControl>
										<FormDescription>
											{t("gameSlugDescription")}
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="categories"
								render={({ field }) => (
									<FormItem>
										<FormLabel>{t("gameCategories")}</FormLabel>
										<FormControl>
											<MultiSelect
												placeholder={t("selectGameCategories")}
												values={field.value || []}
												onValuesChange={field.onChange}
												disabled={isLoadingCategories}
												mode="multiple"
											>
												{!isLoadingCategories &&
													categories?.map((category) => (
														<MultiSelectItem
															key={category.code}
															value={category.code}
															label={category.name}
														>
															{category.name}
														</MultiSelectItem>
													))}
											</MultiSelect>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="gameType"
								render={({ field }) => (
									<FormItem>
										<FormLabel>{t("gameType")}</FormLabel>
										<Select value={field.value} onValueChange={field.onChange}>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder={t("selectGameType")} />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												<SelectItem value={GameType.Iframe}>
													{t("gameTypes.iframe")}
												</SelectItem>
												<SelectItem value={GameType.Download}>
													{t("gameTypes.download")}
												</SelectItem>
												<SelectItem value={GameType.Popup}>
													{t("gameTypes.popup")}
												</SelectItem>
												<SelectItem value={GameType.Placeholder}>
													{t("gameTypes.placeholder")}
												</SelectItem>
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>

							{(formData.gameType === GameType.Iframe ||
								formData.gameType === GameType.Popup) && (
								<FormField
									control={form.control}
									name="gameIframeUrl"
									render={({ field }) => (
										<FormItem className="md:col-span-2">
											<FormLabel className="after:content-['*'] after:ml-0.5 after:text-red-500">
												{t("gameIframeUrl")}
											</FormLabel>
											<FormControl>
												<div className="flex items-center justify-start">
													<Input
														placeholder={t("enterGameIframeUrl")}
														{...field}
														value={field.value || ""}
													/>
													<Button
														className="ml-2"
														type="button"
														onClick={() => handlePreviewIframe(field.value)}
													>
														<Eye className="w-4 h-4" />
														{t("preview")}
													</Button>
												</div>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							)}
						</div>
					</div>

					{gameType === GameType.Download && (
						<div className="bg-card rounded-lg p-6">
							<div className=" mb-6">
								<h2 className="text-xl font-semibold flex items-center gap-2">
									<Icon name="download" size={20} />
									{t("gameDownloadSettings")}
								</h2>
								<FormDescription>
									{t("gameDownloadSettingsDescription")}
								</FormDescription>
							</div>

							<div className="space-y-6">
								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									<FormField
										control={form.control}
										name="gameDownloadSettings.downloadUrls.pc"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="flex items-center gap-1">
													<Icon name="monitor" size={16} />
													{t("pcDownloadLink")}
												</FormLabel>
												<FormControl>
													<Input
														placeholder={t("enterDownloadLink")}
														{...field}
														value={field.value || ""}
														onChange={(e) => {
															field.onChange(e.target.value)
														}}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="gameDownloadSettings.downloadUrls.mac"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="flex items-center gap-1">
													<Icon name="apple" size={16} />
													{t("macDownloadLink")}
												</FormLabel>
												<FormControl>
													<Input
														placeholder={t("enterDownloadLink")}
														{...field}
														value={field.value || ""}
														onChange={(e) => {
															field.onChange(e.target.value)
														}}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="gameDownloadSettings.downloadUrls.android"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="flex items-center gap-1">
													<Icon name="smartphone" size={16} />
													{t("androidDownloadLink")}
												</FormLabel>
												<FormControl>
													<Input
														placeholder={t("enterDownloadLink")}
														{...field}
														value={field.value || ""}
														onChange={(e) => {
															field.onChange(e.target.value)
														}}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="gameDownloadSettings.downloadUrls.ios"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="flex items-center gap-1">
													<Icon name="smartphone" size={16} />
													{t("iosDownloadLink")}
												</FormLabel>
												<FormControl>
													<Input
														placeholder={t("enterDownloadLink")}
														{...field}
														value={field.value || ""}
														onChange={(e) => {
															field.onChange(e.target.value)
														}}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="gameDownloadSettings.downloadUrls.steam"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="flex items-center gap-1">
													<Icon name="gamepad" size={16} />
													{t("steamDownloadLink")}
												</FormLabel>
												<FormControl>
													<Input
														placeholder={t("enterDownloadLink")}
														{...field}
														value={field.value || ""}
														onChange={(e) => {
															field.onChange(e.target.value)
														}}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							</div>
						</div>
					)}

					<div className="bg-card rounded-lg p-6">
						<h2 className="text-xl font-semibold mb-6 flex items-center gap-2">
							<Icon name="image" size={20} />
							{t("gameCoverImage")} & {t("backgroundSettings")}
						</h2>

						<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
							<FormField
								control={form.control}
								name="screenshotUrl"
								render={({ field }) => (
									<FormItem>
										<FormLabel>{t("gameCoverImage")}</FormLabel>
										<FormControl>
											<ImageUploader
												value={field.value || ""}
												onChange={(url) => {
													field.onChange(url)
												}}
												projectId={projectId}
												variant="image"
												accept="image/*"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{formData.gameType === GameType.Placeholder && (
								<div className="space-y-4">
									<FormField
										control={form.control}
										name="backgroundSettings.show"
										render={({ field }) => (
											<FormItem>
												<FormLabel>{t("showBackground")}</FormLabel>
												<FormControl>
													<Switch
														checked={field.value}
														onCheckedChange={(checked) => {
															field.onChange(checked)
														}}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									{backgroundSettings?.show && (
										<>
											<FormField
												control={form.control}
												name="backgroundSettings.type"
												render={({ field }) => (
													<FormItem>
														<FormLabel>{t("backgroundType")}</FormLabel>
														<FormControl>
															<RadioGroup
																value={field.value}
																onValueChange={field.onChange}
																className="flex space-x-4"
															>
																<FormItem className="flex items-center space-x-2 space-y-0">
																	<FormControl>
																		<RadioGroupItem value="image" />
																	</FormControl>
																	<FormLabel className="font-normal">
																		{t("backgroundTypeImage")}
																	</FormLabel>
																	<FormMessage />
																</FormItem>
																<FormItem className="flex items-center space-x-2 space-y-0">
																	<FormControl>
																		<RadioGroupItem value="video" />
																	</FormControl>
																	<FormLabel className="font-normal">
																		{t("backgroundTypeVideo")}
																	</FormLabel>
																	<FormMessage />
																</FormItem>
															</RadioGroup>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>

											{backgroundSettings?.type === "image" && (
												<FormField
													control={form.control}
													name="backgroundSettings.imageUrl"
													render={({ field }) => (
														<FormItem>
															<FormLabel>{t("backgroundImage")}</FormLabel>
															<FormControl>
																<ImageUploader
																	value={field.value || ""}
																	onChange={field.onChange}
																	projectId={projectId}
																	accept="image/*"
																	variant="image"
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>
											)}

											{backgroundSettings?.type === "video" && (
												<FormField
													control={form.control}
													name="backgroundSettings.videoUrl"
													render={({ field }) => (
														<FormItem>
															<FormLabel>{t("backgroundVideo")}</FormLabel>
															<FormControl>
																<Input
																	placeholder={t("enterVideoUrl")}
																	value={field.value || ""}
																	onChange={field.onChange}
																/>
															</FormControl>
															<FormDescription>
																{t("backgroundVideoDescription")}
															</FormDescription>
															<FormMessage />
														</FormItem>
													)}
												/>
											)}
										</>
									)}
								</div>
							)}
						</div>
					</div>

					<div className="bg-card rounded-lg p-6">
						<h2 className="text-xl font-semibold mb-6 flex items-center gap-2">
							<Icon name="tag" size={20} />
							{t("gameTags")}
						</h2>

						<div className="space-y-4">
							<FormField
								control={form.control}
								name="tags"
								render={({ field }) => (
									<FormItem>
										<FormControl>
											<GameTagSelector
												value={field.value || []}
												onChange={field.onChange}
												placeholder={t("enterTag")}
											/>
										</FormControl>
										<FormDescription>
											{t("gameTagsDescription")}
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
					</div>

					<div className="bg-card rounded-lg p-6">
						<h2 className="text-xl font-semibold mb-6 flex items-center gap-2">
							<Icon name="film" size={20} />
							{t("relatedVideos")}
						</h2>

						<div className="space-y-4">
							<div className="flex gap-2">
								<Input
									placeholder={t("enterRelatedVideoUrl")}
									value={newVideoUrl}
									onChange={(e) => setNewVideoUrl(e.target.value)}
									onKeyDown={(e) => {
										if (e.key === "Enter") {
											e.preventDefault()
											handleAddVideo(newVideoUrl)
										}
									}}
								/>
								<Button
									type="button"
									onClick={() => {
										handleAddVideo(newVideoUrl)
									}}
								>
									<Plus className="w-4 h-4 mr-2" />
									{t("addRelatedVideo")}
								</Button>
							</div>

							<div className="space-y-4 mt-4">
								{relatedVideos.map((video, index) => (
									<div
										key={index}
										className="border rounded-md overflow-hidden"
									>
										<div className="grid grid-cols-12 gap-2 p-3">
											<div className="col-span-4 aspect-video">
												<ReactPlayer
													url={video}
													width="100%"
													height="100%"
													controls
													playing={false}
													light={true}
													config={{
														youtube: {
															playerVars: { showinfo: 1 },
														},
													}}
												/>
											</div>
											<div className="col-span-8 flex flex-col justify-between">
												<div className="flex items-start gap-2 overflow-hidden">
													<Video className="w-5 h-5 flex-shrink-0 mt-0.5" />
													<span className="text-sm break-all line-clamp-2">
														{video}
													</span>
												</div>
												<div className="flex justify-end mt-2">
													<Button
														variant="ghost"
														size="sm"
														className="text-destructive hover:text-destructive"
														onClick={() => handleRemoveVideo(video)}
													>
														<Trash className="w-4 h-4 mr-1" />
														{t("delete")}
													</Button>
												</div>
											</div>
										</div>
									</div>
								))}
							</div>
						</div>
					</div>

					<div className="fixed bottom-0 left-0 right-0 bg-background/80 backdrop-blur-sm border-t border-border py-2  px-4 sm:px-6 z-10 shadow-lg">
						<div className="max-w-6xl mx-auto flex justify-end w-full">
							<Button
								type="reset"
								variant="outline"
								loading={isLoading}
								className="mr-3"
							>
								{t("reset")}
							</Button>
							<Button type="submit" loading={isLoading}>
								{isLoading ? t("saving") : t("saveSettings")}
							</Button>
						</div>
					</div>
				</form>
			)}

			<IframePreviewComponent
				iframeUrl={previewIframeUrl}
				isOpen={!!previewIframeUrl}
				onClose={() => setPreviewIframeUrl("")}
			/>
		</Form>
	)
}
