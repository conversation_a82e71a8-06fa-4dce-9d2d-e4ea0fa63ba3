"use client"

import { TranslationProvider } from "@/lib/components/translation/TranslationContext"
import { CircleLoading } from "@lib/components/CircleLoading"
import { getLanguageName, Language, ProjectLanguage } from "@repo/shared-types"
import { Button } from "@repo/ui/components"
import { fetchGet } from "@repo/utils/react"
import { ChevronLeft } from "lucide-react"
import { useTranslations } from "next-intl"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import useSWR from "swr"
import GameForm from "./GameForm/index"

// 项目站点设置类型定义
interface ProjectSiteSettings {
	id: string
	projectId: string
	defaultLocale: string
	languanges: string[]
	logo?: string
	darkLogo?: string
	contactEmail?: string
	icons?: Record<string, any>
	analytics?: Record<string, any>
	socialLinks?: Record<string, any>
	fonts?: Record<string, any>
	theme?: Record<string, any>
	customHeaderContent?: string
	adsTxtContent?: string
}
interface GameContentProps {
	projectId: string
	gameId: string
	defaultLanguage: ProjectLanguage
	languages: Language[]
}

export default function GameContent({
	projectId,
	gameId,
	defaultLanguage,
	languages,
}: GameContentProps) {
	const t = useTranslations("Games")
	const router = useRouter()

	const handleBack = () => {
		router.push(`/project/${projectId}/games`)
	}

	const isNewGame = gameId === "new-game"
	const pageTitle = isNewGame ? t("addGame") : t("editGame")

	return (
		<>
			<div className="flex items-center mb-6">
				<Button
					variant="ghost"
					size="icon"
					onClick={handleBack}
					className="mr-2"
				>
					<ChevronLeft className="w-5 h-5" />
				</Button>
				<h1 className="text-2xl font-bold">{pageTitle}</h1>
			</div>
			<div className="bg-card rounded-lg p-4 mb-8">
				<GameForm
					gameId={gameId}
					projectId={projectId}
					defaultLanguage={defaultLanguage}
					languages={languages}
				/>
			</div>
		</>
	)
}
