"use client"

import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@repo/ui/components"
import { useTranslations } from "next-intl"
import { useState } from "react"
import BasicInfoTab from "./BasicInfoTab"
import ContentTab from "./ContentTab"
import { TranslationProvider } from "@/lib/components/translation/TranslationContext"
import { Language, ProjectLanguage } from "@repo/shared-types"
import MetadataTab from "./MetadataTab"
import RelatedGamesTab from "./RelatedGamesTab"
import GameInfoTab from "./GameInfoTab"
import { useSearchParams } from "next/navigation"

interface GameFormProps {
	projectId: string
	gameId: string
	defaultLanguage: ProjectLanguage
	languages: Language[]
}

export default function GameForm({
	gameId,
	projectId,
	defaultLanguage,
	languages,
}: GameFormProps) {
	const t = useTranslations("Games")
	const searchParams = useSearchParams()
	const tab = searchParams.get("tab")
	const [activeTab, setActiveTab] = useState(tab || "basic")
	const isNewGame = gameId === "new-game"

	return (
		<TranslationProvider
			languages={languages}
			defaultLanguage={defaultLanguage}
			isTranslation={true}
		>
			<div className="space-y-8">
				<Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
					{!isNewGame && (
						<TabsList className="mb-6">
							<TabsTrigger value="basic" className="px-4 py-2">
								{t("basicInfo")}
							</TabsTrigger>
							<TabsTrigger value="gameinfo" className="px-4 py-2">
								{t("gameInfo")}
							</TabsTrigger>
							<TabsTrigger value="content" className="px-4 py-2">
								{t("contentModules")}
							</TabsTrigger>
							<TabsTrigger value="metadata" className="px-4 py-2">
								{t("seoSettings")}
							</TabsTrigger>
							<TabsTrigger value="related" className="px-4 py-2">
								{t("relatedGames")}
							</TabsTrigger>
						</TabsList>
					)}

					<TabsContent value="basic">
						<BasicInfoTab gameId={gameId} projectId={projectId} />
					</TabsContent>

					<TabsContent value="gameinfo">
						<GameInfoTab gameId={gameId} projectId={projectId} />
					</TabsContent>

					<TabsContent value="content">
						<ContentTab
							gameId={gameId}
							projectId={projectId}
							defaultLanguage={defaultLanguage}
							languages={languages}
						/>
					</TabsContent>

					<TabsContent value="metadata">
						<MetadataTab
							gameId={gameId}
							projectId={projectId}
							defaultLanguage={defaultLanguage}
							languages={languages}
						/>
					</TabsContent>

					<TabsContent value="related">
						<RelatedGamesTab
							gameId={gameId}
							projectId={projectId}
							defaultLanguage={defaultLanguage}
							languages={languages}
						/>
					</TabsContent>
				</Tabs>
			</div>
		</TranslationProvider>
	)
}
