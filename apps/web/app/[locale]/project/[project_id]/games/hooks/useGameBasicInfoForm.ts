"use client"

import { useCallback, useState } from "react"
import { toast } from "sonner"

import { useTranslationContext } from "@/lib/components/translation/TranslationContext"
import {
	GameBackgroundSettings,
	GameDownloadSettings,
	GameType,
	ProjectGameStatus,
} from "@repo/shared-types"
import { fetchGet, fetchPost, fetchPut } from "@repo/utils/react"
import useSWR from "swr"
import { useRouter } from "@repo/i18n"
export interface GameBasicInfoData {
	id?: string
	publicGameLibraryId?: string
	name: string
	slug?: string
	categories?: string[]
	userInputText?: string
	isPrimary?: boolean
	settings?: Record<string, any>
	tags?: string[]
	gameType?: GameType
	gameDownloadSettings?: GameDownloadSettings
	backgroundSettings?: GameBackgroundSettings
	gameIframeUrl?: string
	screenshotUrl?: string
	relatedVideos?: { url: string }[]
}

export function useGameBasicInfoForm(projectId: string, gameId: string) {
	const router = useRouter()
	const { currentLanguage } = useTranslationContext()
	const [isSaving, setIsSaving] = useState(false)

	const {
		data,
		error,
		isLoading,
		mutate: refreshFormData,
	} = useSWR<GameBasicInfoData>(
		gameId !== "new-game" ? `/api/project-games?id=${gameId}` : null,
		fetchGet,
		{
			revalidateOnFocus: false,
			revalidateOnReconnect: false,
			onError: (err) => {
				console.error("获取游戏信息失败:", err)
				toast.error("获取游戏信息失败")
			},
		},
	)

	// 保存游戏基本信息
	const saveOrUpdate = useCallback(
		async (editData: GameBasicInfoData) => {
			try {
				let response: any
				if (gameId === "new-game") {
					// 创建新游戏
					response = await fetchPost("/api/project-games/new-game", {
						projectId,
						...editData,
						status: ProjectGameStatus.Completed,
					})
				} else {
					// 更新现有游戏
					response = await fetchPut(`/api/project-games/update-game`, {
						projectId,
						gameId,
						...editData,
						status: ProjectGameStatus.Completed,
					})
				}
				return response
			} catch (error) {
				console.error("保存游戏信息失败:", error)
				throw error
			}
		},
		[gameId, projectId],
	)

	// 保存所有游戏数据
	const handleSave = async (values: GameBasicInfoData) => {
		if (!values) return

		setIsSaving(true)

		try {
			const response = await saveOrUpdate(values)
			if (gameId === "new-game") {
				toast.success("游戏创建成功，请继续完善内容模块、SEO设置")
				setTimeout(() => {
					router.replace(`/project/${projectId}/games/${response.id}?tab=content`)
				}, 1000)
			} else {
				toast.success("游戏更新成功")
			}
		} catch (err) {
			toast.error("保存游戏失败")
			throw err
		} finally {
			setIsSaving(false)
		}
	}

	return {
		isLoading: isLoading || isSaving,
		data,
		error,
		handleSave,
		refreshFormData,
	}
}
