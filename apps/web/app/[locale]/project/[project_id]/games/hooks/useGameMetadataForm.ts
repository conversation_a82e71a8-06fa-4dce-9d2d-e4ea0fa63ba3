"use client"

import {
	MetadataInfo,
	ProjectGameLocaleType,
	ProjectLanguage,
} from "@repo/shared-types"
import { fetchGet, fetchPost } from "@repo/utils/react"
import { useTranslationContext } from "@/lib/components/translation/TranslationContext"
import { useState, useEffect, useCallback } from "react"
import { toast } from "sonner"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import useSWR from "swr"
import { useGameBasicInfoForm } from "./useGameBasicInfoForm"

// 扩展 MetadataInfo 类型，添加额外字段
export type GameMetadata = {
	id?: string
	locale?: ProjectLanguage
	// 标题
	title: string
	// 名称
	name?: string
	// 描述
	description: string
	// 社交分享标题
	ogTitle?: string
	// 社交分享描述
	ogDescription?: string
	// 社交分享图片
	ogImage?: string
	// 社交分享链接
	ogUrl?: string
	// 社交分享类型
	twitterCard?: string
	// 社交分享标题
	twitterTitle?: string
	// 社交分享描述
	twitterDescription?: string
	// 社交分享图片
	twitterImage?: string
}

interface UseGameMetadataFormProps {
	projectId: string
	gameId: string
}

// 定义表单验证模式
export const metadataFormSchema = z.object({
	title: z
		.string()
		.min(1, { message: "元标题不能为空" })
		.max(70, { message: "元标题不能超过70个字符" }),
	name: z.string().optional(),
	description: z
		.string()
		.min(1, { message: "元描述不能为空" })
		.max(200, { message: "元描述不能超过200个字符" }),
	ogTitle: z.string().optional(),
	ogDescription: z.string().optional(),
	ogImage: z.string().optional(),
	ogUrl: z.string().optional(),
	twitterCard: z.string().optional(),
	twitterTitle: z.string().optional(),
	twitterDescription: z.string().optional(),
	twitterImage: z.string().optional(),
})

export type MetadataFormData = z.infer<typeof metadataFormSchema>

export function useGameMetadataForm({
	projectId,
	gameId,
}: UseGameMetadataFormProps) {
	const { currentLanguage } = useTranslationContext()
	const [isSaving, setIsSaving] = useState(false)
	const { data: gameBasicInfo } = useGameBasicInfoForm(projectId, gameId)
	// 获取元数据
	const {
		data: metadata,
		isLoading,
		error,
		mutate: refreshMetadata,
	} = useSWR<GameMetadata>(
		`/api/project-games/content/list?projectId=${projectId}&gameId=${gameId}&locale=${currentLanguage || ProjectLanguage.EN}&type=${ProjectGameLocaleType.Metadata}`,
		(url: string) =>
			fetchGet(url).then((res) => {
				return (
					res && res.length > 0
						? {
								...res[0].content,
								locale: currentLanguage || ProjectLanguage.EN,
								id: res[0].id,
							}
						: null
				) as GameMetadata
			}),
	)

	// 设置表单
	const form = useForm<MetadataFormData>({
		resolver: zodResolver(metadataFormSchema),
		defaultValues: {
			title: "",
			name: gameBasicInfo?.name || "",
			description: "",
			ogTitle: "",
			ogDescription: "",
			ogImage: "",
			ogUrl: "",
			twitterCard: "",
			twitterTitle: "",
			twitterDescription: "",
			twitterImage: "",
		},
	})

	// 当数据加载完成后更新表单
	useEffect(() => {
		if (metadata) {
			form.reset({
				title: metadata.title || "",
				name: metadata.name || gameBasicInfo?.name || "",
				description: metadata.description || "",
				ogTitle: metadata.ogTitle || "",
				ogDescription: metadata.ogDescription || "",
				ogImage: metadata.ogImage || "",
				ogUrl: metadata.ogUrl || "",
				twitterCard: metadata.twitterCard || "",
				twitterTitle: metadata.twitterTitle || "",
				twitterDescription: metadata.twitterDescription || "",
				twitterImage: metadata.twitterImage || "",
			})
		}
	}, [metadata, form])

	// 提交表单
	const handleSubmit = useCallback(
		async (data: MetadataFormData) => {
			setIsSaving(true)
			try {
				const payload = {
					projectId,
					gameId,
					locale: currentLanguage || ProjectLanguage.EN,
					type: ProjectGameLocaleType.Metadata,
					content: data,
				}

				await fetchPost("/api/project-games/content", payload)
				toast.success("保存成功")
				refreshMetadata()
				return true
			} catch (error) {
				console.error("保存元数据失败:", error)
				toast.error("保存失败")
				return false
			} finally {
				setIsSaving(false)
			}
		},
		[projectId, gameId, currentLanguage, refreshMetadata],
	)

	return {
		form,
		isLoading,
		error,
		metadata,
		refreshMetadata,
		handleSubmit,
		isSaving,
	}
}
