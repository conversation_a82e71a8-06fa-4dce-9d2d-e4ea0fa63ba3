// 游戏模板分类
export enum GameTemplateType {
	Single = "single-game",
	GameBox = "game-box",
}

// 部署方式
export enum HostingType {
	Vercel = "vercel",
	SelfHost = "self-host",
}

export enum UserRole {
	// 管理员
	Admin = "ADMIN",
	// 试用版
	User = "v0",
	// 青铜版
	V1 = "v1",
	// 黄金版
	V2 = "v2",
	// 钻石版
	V3 = "v3",
	// 王者版
	V4 = "v4",
}

export enum ServerStatus {
	// 活跃状态
	Active = "ACTIVE",
	// 非活跃状态
	Inactive = "INACTIVE",
	// 维护状态
	Maintenance = "MAINTENANCE",
}

export enum GameCategoryType {
	// 精选游戏
	Featured = "FEATURED",
	// 新游戏
	New = "NEW",
	// 热门游戏
	Popular = "POPULAR",
	// 其他游戏
	Other = "OTHER",
}

export enum PromotionStatus {
	// 待审核
	Pending = "PENDING",
	// 已激活
	Active = "ACTIVE",
	// 已停用
	Inactive = "INACTIVE",
}

export enum GameItemStatus {
	// 原始抓取数据
	Raw = "RAW",
	// 清洗中
	Cleaning = "CLEANING",
	// 清洗失败
	CleanFailed = "CLEAN_FAILED",
	// 清洗完成
	Cleaned = "CLEANED",
	// 已合并
	Merged = "MERGED",
	// 无效数据
	Invalid = "INVALID",
}

export enum AccountStatus {
	// 活跃
	Active = "ACTIVE",
	// 不活跃
	Inactive = "INACTIVE",
	// 被封禁
	Blocked = "BLOCKED",
}

export enum GameStatus {
	// 激活状态
	Active = "ACTIVE",
	// 未激活
	Inactive = "INACTIVE",
	// 待审核
	Pending = "PENDING",
	// 已拒绝
	Rejected = "REJECTED",
}

export enum ProjectGameStatus {
	// 待处理
	Pending = "PENDING",
	// 已完成
	Completed = "COMPLETED",
	// 失败
	Failed = "FAILED",
}

export enum CloudflareAccountStatus {
	// 活跃状态
	Active = "ACTIVE",
	// 非活跃状态
	Inactive = "INACTIVE",
	// 已禁用
	Disabled = "DISABLED",
}

export enum TrendStatus {
  // 待抓取
  Pending = 'PENDING',
  // 抓取中
  Fetching = 'FETCHING',
  // 抓取失败
  Failed = 'FAILED',
  // 抓取成功
  Success = 'SUCCESS',
  // 原始抓取数据
  Raw = "RAW",
  // 清洗中
  Cleaning = "CLEANING",
  // 清洗失败
  CleanFailed = "CLEAN_FAILED",
  // 清洗完成
  Cleaned = "CLEANED",
  // 已合并
  Merged = "MERGED",
  // 无效数据
  Invalid = "INVALID"
}

export enum ProjectGameLocaleType{
	// @see api-types.ts MetadataInfo
	Metadata = "metadata",
	// 游戏内容（包含游戏玩法、攻略、截图、FAQ等，使用json格式存放每种语言的所有内容）
	Content = "content",
	// 游戏基本信息（比如需要国际化的名称，游戏描述等）
	BasicInfo = "basicInfo"
}
